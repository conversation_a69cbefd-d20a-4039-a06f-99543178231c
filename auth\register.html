<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON></title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        
        .page-header {
            text-align: center;
            margin: 120px 0 30px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .register-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 50px;
            align-items: stretch;
        }
        
        .register-form-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .register-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
        }
        
        .form-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .form-header h2 {
            color: #4285F4;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #4285F4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .register-btn {
            background: linear-gradient(to right, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
        }
        
        .rules-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 600px;
        }
        
        .rules-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #ff7aa8, #4285F4);
        }
        
        .rules-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .rules-header {
            flex-shrink: 0;
            margin-bottom: 15px;
        }

        .rules-header h2 {
            color: #ff7aa8;
            font-size: 1.8rem;
            margin-bottom: 0;
        }
        
        .rules-content {
            flex: 1;
            max-height: 900px;
            overflow-y: auto;
            padding-right: 15px;
            padding-bottom: 20px;
        }
        
        .rules-content::-webkit-scrollbar {
            width: 8px;
        }

        .rules-content::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 10px;
            margin: 5px 0;
        }

        .rules-content::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .rules-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #3367D6, #e55a8a);
            transform: scale(1.1);
        }
        
        .rules-content h2 {
            color: #4285F4;
            font-size: 1.4rem;
            margin: 0 0 15px;
            text-align: center;
        }

        .rules-content h3 {
            color: #4285F4;
            font-size: 1.1rem;
            margin: 20px 0 10px;
        }
        
        .rules-content h3:first-child {
            margin-top: 0;
        }
        
        .rules-content p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .rules-content ol {
            padding-left: 20px;
            margin-bottom: 15px;
        }
        
        .rules-content li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .rules-content li ol {
            list-style-type: lower-alpha;
            margin: 10px 0;
        }
        
        .contact-info {
            margin-top: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        
        .contact-info h2 {
            color: #4285F4;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .contact-methods {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        
        .contact-method {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            padding: 20px;
            border-radius: 10px;
            background-color: #f8f9fa;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .contact-method i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #ff7aa8;
        }
        
        .contact-method h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
            color: #333;
        }
        
        .contact-method p,
        .contact-method a {
            color: #666;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .contact-method a:hover {
            color: #4285F4;
        }
        
        .agreement-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .agreement-checkbox input {
            margin-top: 5px;
            margin-right: 10px;
        }
        
        .success-message {
            background-color: rgba(76, 217, 100, 0.1);
            color: #2ca745;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }

        .error-message {
            background-color: rgba(255, 77, 77, 0.1);
            color: #ff4d4d;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading i {
            color: #4285F4;
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .register-container {
                flex-direction: column;
            }
            
            .contact-methods {
                flex-direction: column;
                align-items: center;
            }
            
            .contact-method {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* Hiệu ứng animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .register-form-container, .rules-container, .contact-info {
            animation: fadeIn 0.8s ease-out forwards;
        }
        
        .rules-container {
            animation-delay: 0.2s;
        }
        
        .contact-info {
            animation-delay: 0.4s;
            opacity: 0;
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        /* Course Registration Sections */
        .course-registration-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .python-section {
            background: linear-gradient(135deg, #3776ab 0%, #ffd43b 100%);
            color: white;
        }

        .scratch-section {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
        }

        .stem-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stem-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: gradientShift 8s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .course-intro {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 80px 20px;
        }

        .course-content {
            z-index: 2;
        }

        .course-header {
            margin-bottom: 30px;
        }

        .course-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .course-image {
            width: 120px;
            height: 120px;
            border-radius: 20px;
            object-fit: cover;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .course-image:hover {
            transform: scale(1.05);
        }

        .course-header h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            text-align: center;
            line-height: 1.2;
            background: linear-gradient(45deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.8));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        .course-header h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.5));
            border-radius: 2px;
        }

        .course-description {
            font-size: 1.2rem;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .course-features {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .feature-item i {
            font-size: 1.3rem;
            opacity: 0.8;
        }

        .register-course-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .register-course-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* Python Animation */
        .python-animation {
            position: relative;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 30px;
        }

        .python-code-editor {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 20px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .editor-header {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .editor-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .editor-dot.red { background: #ff5f57; }
        .editor-dot.yellow { background: #ffbd2e; }
        .editor-dot.green { background: #28ca42; }

        .python-code {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .code-line {
            opacity: 0;
            transform: translateX(-20px);
            animation: typeIn 0.8s ease forwards;
            margin-bottom: 5px;
            color: #fff;
        }

        .code-line.comment { color: #6a9955; }
        .code-line.import { color: #c586c0; }
        .code-line.function { color: #dcdcaa; }
        .code-line.string { color: #ce9178; }
        .code-line.keyword { color: #569cd6; }

        .code-line:nth-child(1) { animation-delay: 0.5s; }
        .code-line:nth-child(2) { animation-delay: 1s; }
        .code-line:nth-child(3) { animation-delay: 1.5s; }
        .code-line:nth-child(4) { animation-delay: 2s; }
        .code-line:nth-child(5) { animation-delay: 2.5s; }
        .code-line:nth-child(6) { animation-delay: 3s; }
        .code-line:nth-child(7) { animation-delay: 3.5s; }
        .code-line:nth-child(8) { animation-delay: 4s; }

        @keyframes typeIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .heart-output {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 20px;
            width: 100%;
            max-width: 350px;
            text-align: center;
            opacity: 0;
            animation: fadeInHeart 1s ease forwards;
            animation-delay: 5.5s;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .heart-art {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.2;
            color: #ff6b9d;
            white-space: pre;
        }

        .heart-line {
            opacity: 0;
            animation: heartLineAppear 0.3s ease forwards;
        }

        .heart-line:nth-child(1) { animation-delay: 6s; }
        .heart-line:nth-child(2) { animation-delay: 6.1s; }
        .heart-line:nth-child(3) { animation-delay: 6.2s; }
        .heart-line:nth-child(4) { animation-delay: 6.3s; }
        .heart-line:nth-child(5) { animation-delay: 6.4s; }
        .heart-line:nth-child(6) { animation-delay: 6.5s; }
        .heart-line:nth-child(7) { animation-delay: 6.6s; }
        .heart-line:nth-child(8) { animation-delay: 6.7s; }
        .heart-line:nth-child(9) { animation-delay: 6.8s; }
        .heart-line:nth-child(10) { animation-delay: 6.9s; }
        .heart-line:nth-child(11) { animation-delay: 7s; }
        .heart-line:nth-child(12) { animation-delay: 7.1s; }
        .heart-line:nth-child(13) { animation-delay: 7.2s; }
        .heart-line:nth-child(14) { animation-delay: 7.3s; }
        .heart-line:nth-child(15) { animation-delay: 7.4s; }
        .heart-line:nth-child(16) { animation-delay: 7.5s; }
        .heart-line:nth-child(17) { animation-delay: 7.6s; }
        .heart-line:nth-child(18) { animation-delay: 7.7s; }
        .heart-line:nth-child(19) { animation-delay: 7.8s; }
        .heart-line:nth-child(20) { animation-delay: 7.9s; }

        @keyframes fadeInHeart {
            to { opacity: 1; }
        }

        @keyframes heartLineAppear {
            0% {
                opacity: 0;
                transform: translateX(-20px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .heart-art:hover .heart-line {
            animation: heartBeat 0.5s ease-in-out;
        }

        @keyframes heartBeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Registration Form Slide Animation */
        .registration-form-slide {
            position: fixed;
            top: 0;
            right: -50%;
            width: 50%;
            height: 100vh;
            background: linear-gradient(135deg, #3776ab 0%, #ffd43b 100%);
            z-index: 1000;
            transition: right 0.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
        }

        .registration-form-slide.active {
            right: 0;
        }

        .slide-form-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .slide-form-header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .slide-form-header h3 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .slide-close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .slide-close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* Scratch Animation */
        .scratch-animation {
            position: relative;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .scratch-video-container {
            position: relative;
            width: 100%;
            max-width: 500px;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
            border: 4px solid rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .scratch-video-container:hover {
            transform: scale(1.02);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.5);
        }

        .scratch-video {
            width: 100%;
            height: auto;
            display: block;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
            pointer-events: none;
        }

        .video-play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .video-play-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .video-play-btn i {
            font-size: 2rem;
            color: #ff6b35;
            margin-left: 5px;
        }

        .video-play-btn.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .video-controls {
            position: absolute;
            bottom: 10px;
            right: 10px;
            display: flex;
            gap: 10px;
            z-index: 10;
        }

        .video-control-btn {
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-control-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }

        .scratch-blocks-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            max-width: 350px;
        }

        .scratch-block {
            padding: 8px 16px;
            border-radius: 15px;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
            text-align: center;
            animation: blockPop 0.6s ease forwards;
            opacity: 0;
            transform: scale(0.8) translateY(20px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .scratch-block.orange {
            background: linear-gradient(135deg, #ff8c00, #ff6b35);
            animation-delay: 0.5s;
        }

        .scratch-block.blue {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            animation-delay: 0.8s;
        }

        .scratch-block.purple {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            animation-delay: 1.1s;
        }

        .scratch-block.green {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            animation-delay: 1.4s;
        }

        @keyframes blockPop {
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .scratch-sparkles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            animation: sparkle 2s ease-in-out infinite;
        }

        .sparkle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .sparkle:nth-child(2) { top: 60%; left: 80%; animation-delay: 0.5s; }
        .sparkle:nth-child(3) { top: 80%; left: 20%; animation-delay: 1s; }
        .sparkle:nth-child(4) { top: 30%; left: 70%; animation-delay: 1.5s; }

        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }

        /* STEM Animation */
        .stem-showcase {
            position: relative;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 30px;
        }

        .achievement-images {
            display: flex;
            gap: 25px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 30px;
        }

        .achievement-img {
            position: relative;
            width: 140px;
            height: 140px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
            transition: all 0.4s ease;
            border: 3px solid rgba(255, 255, 255, 0.2);
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        }

        .achievement-img:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .achievement-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .achievement-img:hover img {
            transform: scale(1.1);
        }

        .achievement-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.6));
            color: white;
            padding: 8px;
            font-size: 0.85rem;
            font-weight: 600;
            text-align: center;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .achievement-img:hover .achievement-label {
            transform: translateY(0);
        }

        .achievement-glow {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff6b9d, #c471ed, #12c2e9);
            border-radius: 22px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .achievement-img:hover .achievement-glow {
            opacity: 0.7;
        }

        .stem-icons {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .stem-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            animation: bounce 2s infinite;
            backdrop-filter: blur(10px);
        }

        .stem-icon:nth-child(1) { animation-delay: 0s; }
        .stem-icon:nth-child(2) { animation-delay: 0.5s; }
        .stem-icon:nth-child(3) { animation-delay: 1s; }
        .stem-icon:nth-child(4) { animation-delay: 1.5s; }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* Final Registration Section */
        .final-registration-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            color: white;
            padding: 80px 0;
            position: relative;
        }

        .final-registration-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(155, 89, 182, 0.1) 0%, transparent 50%);
        }

        .registration-content {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .registration-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .registration-header h2 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #3498db, #9b59b6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .registration-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .registration-form-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: white;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input::placeholder,
        .form-group select option {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3498db;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.3);
        }

        .final-submit-btn {
            background: linear-gradient(135deg, #3498db, #9b59b6);
            color: white;
            border: none;
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 0 auto;
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
        }

        .final-submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(52, 152, 219, 0.4);
        }

        /* Registration Modal */
        .registration-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .modal-header h2 {
            color: #333;
            margin: 0;
            font-size: 1.8rem;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-modal:hover {
            background: #f0f0f0;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .required {
            color: #e74c3c;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4285F4;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .cancel-btn, .submit-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background: #f8f9fa;
            color: #6c757d;
        }

        .cancel-btn:hover {
            background: #e9ecef;
        }

        .submit-btn {
            background: #4285F4;
            color: white;
        }

        .submit-btn:hover {
            background: #3367d6;
            transform: translateY(-2px);
        }

        .success-message {
            text-align: center;
            padding: 30px;
        }

        .success-message i {
            font-size: 3rem;
            color: #27ae60;
            margin-bottom: 20px;
        }

        .success-message h3 {
            color: #27ae60;
            margin-bottom: 15px;
        }

        .success-message p {
            color: #666;
            line-height: 1.6;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .course-intro {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .course-header h2 {
                font-size: 2rem;
            }

            .course-icon {
                font-size: 3rem;
            }

            .python-animation,
            .scratch-animation,
            .stem-showcase {
                height: 300px;
            }

            .code-lines {
                position: static;
                margin-top: 20px;
            }

            .achievement-images {
                flex-direction: column;
                align-items: center;
            }

            .achievement-img {
                width: 100px;
                height: 100px;
            }

            .modal-content {
                padding: 20px;
                margin: 20px;
            }

            .final-registration-section {
                padding: 60px 20px;
            }

            .registration-header h2 {
                font-size: 2rem;
            }

            .registration-form-container {
                padding: 30px 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .final-submit-btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="register.html" class="active">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="index.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Course Registration Sections -->

    <!-- Python Course Section -->
    <section class="course-registration-section python-section" id="python-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <img src="../assets/images/courses/python-course.jpg" alt="Python Course" class="course-image">
                        <h2>Python - AI từ Cơ Bản đến Nâng Cao</h2>
                        <p class="course-description">Khám phá thế giới lập trình Python và Trí tuệ nhân tạo với những dự án thực tế hấp dẫn</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh THCS - THPT</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>250,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="scrollToRegistration('python')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="python-animation">
                        <div class="python-code-editor">
                            <div class="editor-header">
                                <div class="editor-dot red"></div>
                                <div class="editor-dot yellow"></div>
                                <div class="editor-dot green"></div>
                            </div>
                            <div class="python-code">
                                <div class="code-line comment"># Vẽ trái tim bằng Python</div>
                                <div class="code-line import"><span class="keyword">import</span> time</div>
                                <div class="code-line function"><span class="keyword">def</span> <span class="function">draw_small_heart</span>():</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">for</span> y <span class="keyword">in</span> range(10, -10, -1):</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;row = <span class="string">""</span></div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">for</span> x <span class="keyword">in</span> range(-20, 20):</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">if</span> ((x*0.07)**2 + (y*0.14)**2 - 1)**3 - (x*0.07)**2 * (y*0.14)**3 <= 0:</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;row += <span class="string">"❤️"</span></div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">else</span>:</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;row += <span class="string">"  "</span></div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">print</span>(row)</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;time.sleep(0.1)</div>
                                <div class="code-line function">draw_small_heart()</div>
                            </div>
                        </div>

                        <div class="heart-output">
                            <div class="heart-art">
                                <div class="heart-line">        ❤️❤️❤️        ❤️❤️❤️        </div>
                                <div class="heart-line">      ❤️❤️❤️❤️❤️    ❤️❤️❤️❤️❤️      </div>
                                <div class="heart-line">    ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️    </div>
                                <div class="heart-line">  ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️  </div>
                                <div class="heart-line">❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️</div>
                                <div class="heart-line">❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️</div>
                                <div class="heart-line">❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️</div>
                                <div class="heart-line">  ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️  </div>
                                <div class="heart-line">    ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️    </div>
                                <div class="heart-line">      ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️      </div>
                                <div class="heart-line">        ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️        </div>
                                <div class="heart-line">          ❤️❤️❤️❤️❤️❤️❤️❤️          </div>
                                <div class="heart-line">            ❤️❤️❤️❤️❤️❤️            </div>
                                <div class="heart-line">              ❤️❤️❤️❤️              </div>
                                <div class="heart-line">                ❤️❤️                </div>
                                <div class="heart-line">                                    </div>
                                <div class="heart-line">                                    </div>
                                <div class="heart-line">                                    </div>
                                <div class="heart-line">                                    </div>
                                <div class="heart-line">                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scratch Course Section -->
    <section class="course-registration-section scratch-section" id="scratch-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <img src="../assets/images/courses/scratch-course.jpg" alt="Scratch Course" class="course-image">
                        <h2>Scratch - Tin Học Cơ Bản</h2>
                        <p class="course-description">Bước đầu tiên vào thế giới lập trình với Scratch - ngôn ngữ lập trình trực quan và thú vị</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh Tiểu học</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>300,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="scrollToRegistration('scratch')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="scratch-animation">
                        <div class="scratch-video-container">
                            <video class="scratch-video" id="scratchVideo" loop>
                                <source src="../assets/videos/scratch-demo.mp4" type="video/mp4">
                                Video demo Scratch
                            </video>
                            <div class="video-overlay"></div>
                            <div class="video-play-btn" id="playBtn" onclick="toggleVideo()">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-controls">
                                <button class="video-control-btn" onclick="toggleMute()" id="muteBtn">
                                    <i class="fas fa-volume-up"></i>
                                </button>
                                <button class="video-control-btn" onclick="toggleFullscreen()">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>

                        <div class="scratch-blocks-container">
                            <div class="scratch-block orange">when clicked</div>
                            <div class="scratch-block blue">move 10 steps</div>
                            <div class="scratch-block purple">say "Hello!"</div>
                            <div class="scratch-block green">repeat 10</div>
                        </div>

                        <div class="scratch-sparkles">
                            <div class="sparkle"></div>
                            <div class="sparkle"></div>
                            <div class="sparkle"></div>
                            <div class="sparkle"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- STEM Course Section -->
    <section class="course-registration-section stem-section" id="stem-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <img src="../assets/images/courses/stem-course.jpg" alt="STEM Course" class="course-image">
                        <h2>Hỗ Trợ Nghiên Cứu KHKT - STEM</h2>
                        <p class="course-description">Phát triển tư duy khoa học và kỹ thuật thông qua các dự án nghiên cứu thực tế</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh THCS - THPT</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>350,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="scrollToRegistration('stem')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="stem-showcase">
                        <div class="achievement-images">
                            <div class="achievement-img">
                                <div class="achievement-glow"></div>
                                <img src="../assets/images/achievements/covid-app-award.jpg" alt="COVID App Award">
                                <div class="achievement-label">🥇 Giải Nhất Quốc gia</div>
                            </div>
                            <div class="achievement-img">
                                <div class="achievement-glow"></div>
                                <img src="../assets/images/achievements/heart-disease-prediction-1.jpg" alt="Heart Disease Prediction">
                                <div class="achievement-label">🥈 Giải Nhì Tỉnh</div>
                            </div>
                            <div class="achievement-img">
                                <div class="achievement-glow"></div>
                                <img src="../assets/images/achievements/forest-fire-detection-1.jpg" alt="Forest Fire Detection">
                                <div class="achievement-label">🥉 Giải Ba Tỉnh</div>
                            </div>
                        </div>
                        <div class="stem-icons">
                            <div class="stem-icon"><i class="fas fa-flask"></i></div>
                            <div class="stem-icon"><i class="fas fa-robot"></i></div>
                            <div class="stem-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="stem-icon"><i class="fas fa-brain"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final Registration Section -->
    <section class="final-registration-section" id="finalRegistration">
        <div class="container">
            <div class="registration-content">
                <div class="registration-header">
                    <h2>Đăng Ký Khóa Học</h2>
                    <p>Điền thông tin để bắt đầu hành trình học tập cùng chúng tôi</p>
                </div>

                <div class="registration-form-container">
                    <form id="finalRegistrationForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="finalStudentName">Tên Học Viên <span class="required">*</span></label>
                                <input type="text" id="finalStudentName" name="studentName" required>
                            </div>

                            <div class="form-group">
                                <label for="finalBirthDate">Ngày Sinh <span class="required">*</span></label>
                                <input type="date" id="finalBirthDate" name="birthDate" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="finalParentName">Tên Phụ Huynh <span class="required">*</span></label>
                                <input type="text" id="finalParentName" name="parentName" required>
                            </div>

                            <div class="form-group">
                                <label for="finalParentPhone">Số Điện Thoại Phụ Huynh <span class="required">*</span></label>
                                <input type="tel" id="finalParentPhone" name="parentPhone" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="finalCourseSelect">Chọn Khóa Học <span class="required">*</span></label>
                            <select id="finalCourseSelect" name="course" required>
                                <option value="">-- Chọn khóa học --</option>
                                <option value="python">Python - AI từ Cơ Bản đến Nâng Cao (250,000 VNĐ/tháng)</option>
                                <option value="scratch">Scratch - Tin Học Cơ Bản (300,000 VNĐ/tháng)</option>
                                <option value="stem">Hỗ Trợ Nghiên Cứu KHKT - STEM (350,000 VNĐ/tháng)</option>
                            </select>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="final-submit-btn">
                                <i class="fas fa-paper-plane"></i>
                                Gửi Đăng Ký
                            </button>
                        </div>
                    </form>

                    <div class="success-message" id="finalSuccessMessage" style="display:none;">
                        <i class="fas fa-check-circle"></i>
                        <h3>Đăng ký thành công!</h3>
                        <p>Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất để xác nhận và cung cấp thông tin chi tiết về khóa học.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Registration Form Modal -->
    <div class="registration-modal" id="registrationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Đăng Ký Khóa Học</h2>
                <button class="close-modal" onclick="hideRegistrationForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="registrationForm">
                <input type="hidden" id="selectedCourse" name="course">

                <div class="form-group">
                    <label for="studentName">Tên Học Viên <span class="required">*</span></label>
                    <input type="text" id="studentName" name="studentName" required>
                </div>

                <div class="form-group">
                    <label for="birthDate">Ngày Tháng Năm Sinh <span class="required">*</span></label>
                    <input type="date" id="birthDate" name="birthDate" required>
                </div>

                <div class="form-group">
                    <label for="parentName">Tên Phụ Huynh <span class="required">*</span></label>
                    <input type="text" id="parentName" name="parentName" required>
                </div>

                <div class="form-group">
                    <label for="parentPhone">Số Điện Thoại Phụ Huynh <span class="required">*</span></label>
                    <input type="tel" id="parentPhone" name="parentPhone" required>
                </div>

                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="hideRegistrationForm()">Hủy</button>
                    <button type="submit" class="submit-btn">Gửi Đăng Ký</button>
                </div>
            </form>

            <div class="success-message" id="successMessage" style="display:none;">
                <i class="fas fa-check-circle"></i>
                <h3>Đăng ký thành công!</h3>
                <p>Chúng tôi sẽ liên hệ với bạn sớm để xác nhận và cung cấp thông tin chi tiết.</p>
            </div>
        </div>
    </div>

    <!-- Slide Registration Form -->
    <div class="registration-form-slide" id="slideForm">
        <button class="slide-close-btn" onclick="hideSlideForm()">
            <i class="fas fa-times"></i>
        </button>

        <div class="slide-form-content">
            <div class="slide-form-header">
                <h3 id="slideFormTitle">Đăng Ký Khóa Học</h3>
                <p>Điền thông tin để đăng ký ngay!</p>
            </div>

            <form id="slideRegistrationForm">
                <input type="hidden" id="slideSelectedCourse" name="course">

                <div class="form-group">
                    <label for="slideStudentName">Tên Học Viên <span class="required">*</span></label>
                    <input type="text" id="slideStudentName" name="studentName" required>
                </div>

                <div class="form-group">
                    <label for="slideBirthDate">Ngày Sinh <span class="required">*</span></label>
                    <input type="date" id="slideBirthDate" name="birthDate" required>
                </div>

                <div class="form-group">
                    <label for="slideParentName">Tên Phụ Huynh <span class="required">*</span></label>
                    <input type="text" id="slideParentName" name="parentName" required>
                </div>

                <div class="form-group">
                    <label for="slideParentPhone">SĐT Phụ Huynh <span class="required">*</span></label>
                    <input type="tel" id="slideParentPhone" name="parentPhone" required>
                </div>

                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="hideSlideForm()">Hủy</button>
                    <button type="submit" class="submit-btn">Gửi Đăng Ký</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getFirestore, collection, addDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Course titles mapping
        const courseTitles = {
            'python': 'Python - AI từ Cơ Bản đến Nâng Cao',
            'scratch': 'Scratch - Tin Học Cơ Bản',
            'stem': 'Hỗ Trợ Nghiên Cứu KHKT - STEM'
        };

        // Show registration form modal
        window.showRegistrationForm = function(courseType) {
            const modal = document.getElementById('registrationModal');
            const modalTitle = document.getElementById('modalTitle');
            const selectedCourse = document.getElementById('selectedCourse');

            modalTitle.textContent = `Đăng Ký - ${courseTitles[courseType]}`;
            selectedCourse.value = courseType;
            modal.style.display = 'block';

            // Reset form and hide messages
            document.getElementById('registrationForm').reset();
            document.getElementById('successMessage').style.display = 'none';
        };

        // Hide registration form modal
        window.hideRegistrationForm = function() {
            const modal = document.getElementById('registrationModal');
            modal.style.display = 'none';
        };

        // Show slide form
        window.showSlideForm = function(courseType) {
            const slideForm = document.getElementById('slideForm');
            const slideFormTitle = document.getElementById('slideFormTitle');
            const slideSelectedCourse = document.getElementById('slideSelectedCourse');

            slideFormTitle.textContent = `Đăng Ký - ${courseTitles[courseType]}`;
            slideSelectedCourse.value = courseType;
            slideForm.classList.add('active');

            // Reset form
            document.getElementById('slideRegistrationForm').reset();
        };

        // Hide slide form
        window.hideSlideForm = function() {
            const slideForm = document.getElementById('slideForm');
            slideForm.classList.remove('active');
        };

        // Scroll to registration section
        window.scrollToRegistration = function(courseType) {
            const finalRegistration = document.getElementById('finalRegistration');
            const courseSelect = document.getElementById('finalCourseSelect');

            // Set the course in the select
            courseSelect.value = courseType;

            // Smooth scroll to registration section
            finalRegistration.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Add highlight effect
            finalRegistration.style.animation = 'highlightSection 2s ease-in-out';
            setTimeout(() => {
                finalRegistration.style.animation = '';
            }, 2000);
        };

        // Video control functions
        window.toggleVideo = function() {
            const video = document.getElementById('scratchVideo');
            const playBtn = document.getElementById('playBtn');

            if (video.paused) {
                video.play();
                playBtn.innerHTML = '<i class="fas fa-pause"></i>';
            } else {
                video.pause();
                playBtn.innerHTML = '<i class="fas fa-play"></i>';
            }
        };

        window.toggleMute = function() {
            const video = document.getElementById('scratchVideo');
            const muteBtn = document.getElementById('muteBtn');

            if (video.muted) {
                video.muted = false;
                muteBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
            } else {
                video.muted = true;
                muteBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
            }
        };

        window.toggleFullscreen = function() {
            const video = document.getElementById('scratchVideo');

            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        };

        // Close modal when clicking outside
        document.getElementById('registrationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideRegistrationForm();
            }
        });

        // Handle form submission
        const form = document.getElementById('registrationForm');
        const successMessage = document.getElementById('successMessage');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                // Prepare form data
                const formData = {
                    studentName: document.getElementById('studentName').value,
                    birthDate: document.getElementById('birthDate').value,
                    parentName: document.getElementById('parentName').value,
                    parentPhone: document.getElementById('parentPhone').value,
                    course: document.getElementById('selectedCourse').value,
                    courseName: courseTitles[document.getElementById('selectedCourse').value],
                    registrationDate: new Date().toISOString(),
                    status: 'pending' // pending, approved, rejected
                };

                // Submit to Firebase
                const docRef = await addDoc(collection(db, "course_registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);

                // Hide form and show success message
                form.style.display = 'none';
                successMessage.style.display = 'block';

                // Auto close modal after 3 seconds
                setTimeout(() => {
                    hideRegistrationForm();
                    form.style.display = 'block';
                    successMessage.style.display = 'none';
                    form.reset();
                }, 3000);

            } catch (error) {
                console.error("Error processing registration: ", error);
                alert(`Lỗi: ${error.message}`);
            }
        });

        // Handle slide form submission
        const slideForm = document.getElementById('slideRegistrationForm');

        slideForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                // Prepare form data
                const formData = {
                    studentName: document.getElementById('slideStudentName').value,
                    birthDate: document.getElementById('slideBirthDate').value,
                    parentName: document.getElementById('slideParentName').value,
                    parentPhone: document.getElementById('slideParentPhone').value,
                    course: document.getElementById('slideSelectedCourse').value,
                    courseName: courseTitles[document.getElementById('slideSelectedCourse').value],
                    registrationDate: new Date().toISOString(),
                    status: 'pending' // pending, approved, rejected
                };

                // Submit to Firebase
                const docRef = await addDoc(collection(db, "course_registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);

                // Show success and close form
                alert('Đăng ký thành công! Chúng tôi sẽ liên hệ với bạn sớm.');
                hideSlideForm();

            } catch (error) {
                console.error("Error processing registration: ", error);
                alert(`Lỗi: ${error.message}`);
            }
        });

        // Smooth scrolling for course sections
        function scrollToCourse(courseId) {
            document.getElementById(courseId + '-registration').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Handle final form submission
        const finalForm = document.getElementById('finalRegistrationForm');
        const finalSuccessMessage = document.getElementById('finalSuccessMessage');

        finalForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                // Prepare form data
                const formData = {
                    studentName: document.getElementById('finalStudentName').value,
                    birthDate: document.getElementById('finalBirthDate').value,
                    parentName: document.getElementById('finalParentName').value,
                    parentPhone: document.getElementById('finalParentPhone').value,
                    course: document.getElementById('finalCourseSelect').value,
                    courseName: courseTitles[document.getElementById('finalCourseSelect').value],
                    registrationDate: new Date().toISOString(),
                    status: 'pending'
                };

                // Submit to Firebase
                const docRef = await addDoc(collection(db, "course_registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);

                // Hide form and show success message
                finalForm.style.display = 'none';
                finalSuccessMessage.style.display = 'block';

                // Reset form after 5 seconds
                setTimeout(() => {
                    finalForm.style.display = 'block';
                    finalSuccessMessage.style.display = 'none';
                    finalForm.reset();
                }, 5000);

            } catch (error) {
                console.error("Error processing registration: ", error);
                alert(`Lỗi: ${error.message}`);
            }
        });

        // Add highlight animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes highlightSection {
                0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
                50% { box-shadow: 0 0 0 20px rgba(52, 152, 219, 0.3); }
                100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
            }
        `;
        document.head.appendChild(style);

        // Check URL parameters for direct course access
        const urlParams = new URLSearchParams(window.location.search);
        const course = urlParams.get('course');
        if (course && courseTitles[course]) {
            setTimeout(() => {
                scrollToRegistration(course);
            }, 500);
        }
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html> 