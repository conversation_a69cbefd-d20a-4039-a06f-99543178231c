<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON></title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="72x72" href="../assets/images/favicon.png">
    <link rel="apple-touch-icon" sizes="114x114" href="../assets/images/favicon.png">

    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        
        .page-header {
            text-align: center;
            margin: 120px 0 30px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .register-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 50px;
            align-items: stretch;
        }
        
        .register-form-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .register-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
        }
        
        .form-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .form-header h2 {
            color: #4285F4;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        
        .form-header p {
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #4285F4;
            outline: none;
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
        }
        
        .register-btn {
            background: linear-gradient(to right, #4285F4, #ff7aa8);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.4);
        }
        
        .rules-container {
            flex: 1;
            min-width: 320px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 600px;
        }
        
        .rules-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(to bottom, #ff7aa8, #4285F4);
        }
        
        .rules-header {
            margin-bottom: 25px;
            text-align: center;
        }
        
        .rules-header {
            flex-shrink: 0;
            margin-bottom: 15px;
        }

        .rules-header h2 {
            color: #ff7aa8;
            font-size: 1.8rem;
            margin-bottom: 0;
        }
        
        .rules-content {
            flex: 1;
            max-height: 900px;
            overflow-y: auto;
            padding-right: 15px;
            padding-bottom: 20px;
        }
        
        .rules-content::-webkit-scrollbar {
            width: 8px;
        }

        .rules-content::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 10px;
            margin: 5px 0;
        }

        .rules-content::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #4285F4, #ff7aa8);
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .rules-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #3367D6, #e55a8a);
            transform: scale(1.1);
        }
        
        .rules-content h2 {
            color: #4285F4;
            font-size: 1.4rem;
            margin: 0 0 15px;
            text-align: center;
        }

        .rules-content h3 {
            color: #4285F4;
            font-size: 1.1rem;
            margin: 20px 0 10px;
        }
        
        .rules-content h3:first-child {
            margin-top: 0;
        }
        
        .rules-content p {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .rules-content ol {
            padding-left: 20px;
            margin-bottom: 15px;
        }
        
        .rules-content li {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .rules-content li ol {
            list-style-type: lower-alpha;
            margin: 10px 0;
        }
        
        .contact-info {
            margin-top: 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        
        .contact-info h2 {
            color: #4285F4;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .contact-methods {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        
        .contact-method {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
            padding: 20px;
            border-radius: 10px;
            background-color: #f8f9fa;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .contact-method:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        .contact-method i {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #ff7aa8;
        }
        
        .contact-method h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
            color: #333;
        }
        
        .contact-method p,
        .contact-method a {
            color: #666;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .contact-method a:hover {
            color: #4285F4;
        }
        
        .agreement-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .agreement-checkbox input {
            margin-top: 5px;
            margin-right: 10px;
        }
        
        .success-message {
            background-color: rgba(76, 217, 100, 0.1);
            color: #2ca745;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }

        .error-message {
            background-color: rgba(255, 77, 77, 0.1);
            color: #ff4d4d;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-weight: 500;
            animation: fadeIn 0.5s ease-out forwards;
            display: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading i {
            color: #4285F4;
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .register-container {
                flex-direction: column;
            }
            
            .contact-methods {
                flex-direction: column;
                align-items: center;
            }
            
            .contact-method {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* Hiệu ứng animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .register-form-container, .rules-container, .contact-info {
            animation: fadeIn 0.8s ease-out forwards;
        }
        
        .rules-container {
            animation-delay: 0.2s;
        }
        
        .contact-info {
            animation-delay: 0.4s;
            opacity: 0;
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        /* Course Registration Sections */
        .course-registration-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .python-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .scratch-section {
            background: linear-gradient(135deg, #764ba2 0%, #f093fb 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .stem-section {
            background: linear-gradient(135deg, #f093fb 0%, #ff6b9d 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .python-section::before,
        .scratch-section::before,
        .stem-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 40%);
            animation: gradientShift 10s ease-in-out infinite;
            z-index: 1;
        }

        .python-section::after,
        .scratch-section::after,
        .stem-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 2px,
                    rgba(255, 255, 255, 0.02) 2px,
                    rgba(255, 255, 255, 0.02) 4px
                );
            opacity: 0.3;
            z-index: 1;
        }

        @keyframes gradientShift {
            0%, 100% {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
            25% {
                opacity: 0.8;
                transform: scale(1.05) rotate(1deg);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1) rotate(0deg);
            }
            75% {
                opacity: 0.8;
                transform: scale(1.05) rotate(-1deg);
            }
        }

        .course-intro {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
            max-width: 1300px;
            margin: 0 auto;
            padding: 100px 30px;
            position: relative;
            z-index: 2;
        }

        .course-content {
            z-index: 3;
            position: relative;
        }

        .course-header {
            margin-bottom: 40px;
            text-align: center;
        }

        .course-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .course-image {
            width: 140px;
            height: 140px;
            border-radius: 25px;
            object-fit: cover;
            margin: 0 auto 25px;
            box-shadow:
                0 15px 40px rgba(0, 0, 0, 0.3),
                0 5px 15px rgba(0, 0, 0, 0.2);
            border: 4px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: block;
        }

        .course-image:hover {
            transform: scale(1.08) translateY(-5px);
            box-shadow:
                0 20px 50px rgba(0, 0, 0, 0.4),
                0 8px 25px rgba(0, 0, 0, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .course-header h2 {
            font-size: 3.2rem;
            font-weight: 800;
            margin-bottom: 25px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            line-height: 1.1;
            background: linear-gradient(45deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.9));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            letter-spacing: -1px;
        }

        .course-header h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.6));
            border-radius: 2px;
            box-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .course-description {
            font-size: 1.3rem;
            opacity: 0.95;
            line-height: 1.7;
            margin-bottom: 35px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            font-weight: 300;
            text-align: center;
        }

        .course-features {
            display: flex;
            flex-direction: column;
            gap: 18px;
            margin-bottom: 45px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 18px;
            font-size: 1.15rem;
            background: rgba(255, 255, 255, 0.15);
            padding: 18px 25px;
            border-radius: 15px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .feature-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(10px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .feature-item i {
            font-size: 1.4rem;
            opacity: 0.9;
            color: rgba(255, 255, 255, 0.9);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .register-course-btn {
            background: rgba(255, 255, 255, 0.25);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.4);
            padding: 18px 40px;
            border-radius: 50px;
            font-size: 1.2rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(15px);
            display: inline-flex;
            align-items: center;
            gap: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .register-course-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .register-course-btn:hover::before {
            left: 100%;
        }

        .register-course-btn:hover {
            background: rgba(255, 255, 255, 0.35);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-4px) scale(1.02);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        /* Python Animation */
        .python-animation {
            position: relative;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 30px;
        }

        .python-code-editor {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 20px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .editor-header {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .editor-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .editor-dot.red { background: #ff5f57; }
        .editor-dot.yellow { background: #ffbd2e; }
        .editor-dot.green { background: #28ca42; }

        .python-code {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .code-line {
            opacity: 0;
            transform: translateX(-20px);
            animation: typeIn 0.8s ease forwards;
            margin-bottom: 5px;
            color: #fff;
        }

        .code-line.comment { color: #6a9955; }
        .code-line.import { color: #c586c0; }
        .code-line.function { color: #dcdcaa; }
        .code-line.string { color: #ce9178; }
        .code-line.keyword { color: #569cd6; }

        .code-line:nth-child(1) { animation-delay: 0.3s; }
        .code-line:nth-child(2) { animation-delay: 0.5s; }
        .code-line:nth-child(3) { animation-delay: 0.7s; }
        .code-line:nth-child(4) { animation-delay: 0.9s; }
        .code-line:nth-child(5) { animation-delay: 1.1s; }
        .code-line:nth-child(6) { animation-delay: 1.3s; }
        .code-line:nth-child(7) { animation-delay: 1.5s; }
        .code-line:nth-child(8) { animation-delay: 1.7s; }
        .code-line:nth-child(9) { animation-delay: 1.9s; }
        .code-line:nth-child(10) { animation-delay: 2.1s; }
        .code-line:nth-child(11) { animation-delay: 2.3s; }

        @keyframes typeIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .heart-output {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 20px;
            width: 100%;
            max-width: 350px;
            text-align: center;
            opacity: 0;
            animation: fadeInHeart 0.8s ease forwards;
            animation-delay: 2.8s;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .heart-art {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.2;
            color: #ff6b9d;
            white-space: pre;
        }

        .heart-line {
            opacity: 0;
            animation: heartLineAppear 0.3s ease forwards;
        }

        .heart-line:nth-child(1) { animation-delay: 3.2s; }
        .heart-line:nth-child(2) { animation-delay: 3.3s; }
        .heart-line:nth-child(3) { animation-delay: 3.4s; }
        .heart-line:nth-child(4) { animation-delay: 3.5s; }
        .heart-line:nth-child(5) { animation-delay: 3.6s; }
        .heart-line:nth-child(6) { animation-delay: 3.7s; }
        .heart-line:nth-child(7) { animation-delay: 3.8s; }
        .heart-line:nth-child(8) { animation-delay: 3.9s; }
        .heart-line:nth-child(9) { animation-delay: 4s; }
        .heart-line:nth-child(10) { animation-delay: 4.1s; }
        .heart-line:nth-child(11) { animation-delay: 4.2s; }
        .heart-line:nth-child(12) { animation-delay: 4.3s; }

        @keyframes fadeInHeart {
            to { opacity: 1; }
        }

        @keyframes heartLineAppear {
            0% {
                opacity: 0;
                transform: translateX(-20px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .heart-art:hover .heart-line {
            animation: heartBeat 0.5s ease-in-out;
        }

        @keyframes heartBeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Registration Form Slide Animation */
        .registration-form-slide {
            position: fixed;
            top: 0;
            right: -50%;
            width: 50%;
            height: 100vh;
            background: linear-gradient(135deg, #3776ab 0%, #ffd43b 100%);
            z-index: 1000;
            transition: right 0.5s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
        }

        .registration-form-slide.active {
            right: 0;
        }

        .slide-form-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .slide-form-header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        .slide-form-header h3 {
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .slide-close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .slide-close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* Scratch Animation */
        .scratch-animation {
            position: relative;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .scratch-video-container {
            position: relative;
            width: 100%;
            max-width: 500px;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
            border: 4px solid rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .scratch-video-container:hover {
            transform: scale(1.02);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.5);
        }

        .scratch-video {
            width: 100%;
            height: auto;
            display: block;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
            pointer-events: none;
        }

        .video-play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .video-play-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .video-play-btn i {
            font-size: 2rem;
            color: #ff6b35;
            margin-left: 5px;
        }

        .video-play-btn.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .video-controls {
            position: absolute;
            bottom: 10px;
            right: 10px;
            display: flex;
            gap: 10px;
            z-index: 10;
        }

        .video-control-btn {
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.7);
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-control-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }

        .scratch-blocks-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            max-width: 350px;
        }

        .scratch-block {
            padding: 8px 16px;
            border-radius: 15px;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
            text-align: center;
            animation: blockPop 0.6s ease forwards;
            opacity: 0;
            transform: scale(0.8) translateY(20px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .scratch-block.orange {
            background: linear-gradient(135deg, #ff8c00, #ff6b35);
            animation-delay: 0.5s;
        }

        .scratch-block.blue {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            animation-delay: 0.8s;
        }

        .scratch-block.purple {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            animation-delay: 1.1s;
        }

        .scratch-block.green {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            animation-delay: 1.4s;
        }

        @keyframes blockPop {
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .scratch-sparkles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            animation: sparkle 2s ease-in-out infinite;
        }

        .sparkle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .sparkle:nth-child(2) { top: 60%; left: 80%; animation-delay: 0.5s; }
        .sparkle:nth-child(3) { top: 80%; left: 20%; animation-delay: 1s; }
        .sparkle:nth-child(4) { top: 30%; left: 70%; animation-delay: 1.5s; }

        @keyframes sparkle {
            0%, 100% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
        }

        /* STEM Animation */
        .stem-showcase {
            position: relative;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 30px;
        }

        .achievement-images {
            display: flex;
            gap: 25px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 30px;
        }

        .achievement-img {
            position: relative;
            width: 140px;
            height: 140px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
            transition: all 0.4s ease;
            border: 3px solid rgba(255, 255, 255, 0.2);
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        }

        .achievement-img:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .achievement-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .achievement-img:hover img {
            transform: scale(1.1);
        }

        .achievement-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.6));
            color: white;
            padding: 8px;
            font-size: 0.85rem;
            font-weight: 600;
            text-align: center;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .achievement-img:hover .achievement-label {
            transform: translateY(0);
        }

        .achievement-glow {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff6b9d, #c471ed, #12c2e9);
            border-radius: 22px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .achievement-img:hover .achievement-glow {
            opacity: 0.7;
        }

        .stem-icons {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .stem-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            animation: bounce 2s infinite;
            backdrop-filter: blur(10px);
        }

        .stem-icon:nth-child(1) { animation-delay: 0s; }
        .stem-icon:nth-child(2) { animation-delay: 0.5s; }
        .stem-icon:nth-child(3) { animation-delay: 1s; }
        .stem-icon:nth-child(4) { animation-delay: 1.5s; }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        /* Final Registration Section */
        .final-registration-section {
            background: linear-gradient(135deg, #7209b7 0%, #533483 25%, #0f3460 50%, #16213e 75%, #1a1a2e 100%);
            color: white;
            padding: 120px 0;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .final-registration-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(114, 9, 183, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(83, 52, 131, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(15, 52, 96, 0.1) 0%, transparent 40%),
                radial-gradient(circle at 60% 20%, rgba(26, 26, 46, 0.1) 0%, transparent 30%);
            animation: gradientShift 12s ease-in-out infinite;
        }

        .final-registration-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 2px,
                    rgba(255, 255, 255, 0.03) 2px,
                    rgba(255, 255, 255, 0.03) 4px
                );
            opacity: 0.4;
        }

        .registration-content {
            max-width: 900px;
            margin: 0 auto;
            position: relative;
            z-index: 3;
            padding: 0 20px;
        }

        .registration-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .registration-header h2 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 25px;
            background: linear-gradient(45deg, #ffffff, rgba(255, 255, 255, 0.8));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            position: relative;
            letter-spacing: -1px;
        }

        .registration-header h2::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.6));
            border-radius: 2px;
            box-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .registration-header p {
            font-size: 1.4rem;
            opacity: 0.95;
            max-width: 650px;
            margin: 0 auto;
            line-height: 1.6;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .registration-form-container {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border-radius: 30px;
            padding: 60px;
            border: 2px solid rgba(255, 255, 255, 0.25);
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .registration-form-container:hover {
            transform: translateY(-5px);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.15);
        }

        .registration-form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 30px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 12px;
            font-weight: 600;
            color: white;
            font-size: 1.1rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 20px 25px;
            border: 2px solid rgba(255, 255, 255, 0.25);
            border-radius: 18px;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.12);
            color: white;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-sizing: border-box;
            font-weight: 400;
            backdrop-filter: blur(10px);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
            font-weight: 300;
        }

        .form-group select option {
            color: #333;
            background: white;
            font-weight: 400;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
            box-shadow:
                0 0 30px rgba(255, 255, 255, 0.2),
                0 10px 30px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transform: translateY(-3px);
        }

        .form-group input:hover,
        .form-group select:hover {
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .final-submit-btn {
            background: linear-gradient(135deg, #ff6b9d, #c471ed, #12c2e9);
            color: white;
            border: none;
            padding: 22px 60px;
            border-radius: 50px;
            font-size: 1.3rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 40px auto 0;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.2),
                0 8px 25px rgba(255, 107, 157, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-transform: uppercase;
            letter-spacing: 1.5px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .final-submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .final-submit-btn:hover::before {
            left: 100%;
        }

        .final-submit-btn:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 12px 35px rgba(255, 107, 157, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .final-submit-btn:active {
            transform: translateY(-3px) scale(1.01);
        }

        .final-submit-btn i {
            font-size: 1.2rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        /* Registration Modal */
        .registration-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .modal-header h2 {
            color: #333;
            margin: 0;
            font-size: 1.8rem;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-modal:hover {
            background: #f0f0f0;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .required {
            color: #e74c3c;
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4285F4;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .cancel-btn, .submit-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background: #f8f9fa;
            color: #6c757d;
        }

        .cancel-btn:hover {
            background: #e9ecef;
        }

        .submit-btn {
            background: #4285F4;
            color: white;
        }

        .submit-btn:hover {
            background: #3367d6;
            transform: translateY(-2px);
        }

        .success-message {
            text-align: center;
            padding: 30px;
        }

        .success-message i {
            font-size: 3rem;
            color: #27ae60;
            margin-bottom: 20px;
        }

        .success-message h3 {
            color: #27ae60;
            margin-bottom: 15px;
        }

        .success-message p {
            color: #666;
            line-height: 1.6;
        }

        /* Courses Section */
        .courses-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            position: relative;
            overflow: hidden;
            color: white;
        }

        .courses-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 30%, rgba(26, 26, 46, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(15, 52, 96, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(83, 52, 131, 0.1) 0%, transparent 50%);
            animation: backgroundMove 30s linear infinite;
        }

        .courses-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255, 255, 255, 0.02) 2px, rgba(255, 255, 255, 0.02) 4px),
                repeating-linear-gradient(-45deg, transparent, transparent 3px, rgba(255, 255, 255, 0.01) 3px, rgba(255, 255, 255, 0.01) 6px);
            background-size: 80px 80px, 50px 50px;
            animation: backgroundMove 25s linear infinite reverse;
            opacity: 0.6;
        }

        .courses-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .courses-title {
            font-size: 2.8rem;
            margin-bottom: 20px;
            color: white;
            position: relative;
            display: inline-block;
        }

        .courses-title .gradient-text {
            background: linear-gradient(135deg, #ffd700, #ff8c00, #ff6b35);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title-decoration {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .decoration-line {
            width: 50px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #ffd700, transparent);
        }

        .title-decoration i {
            color: #ffd700;
            font-size: 1.5rem;
        }

        .courses-subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 600px;
            margin: 0 auto;
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .course-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .course-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .course-card:hover::before {
            opacity: 1;
        }

        .course-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .course-icon {
            font-size: 3rem;
            color: #ffd700;
        }

        .course-badge {
            background: linear-gradient(135deg, #ff6b35, #ff8c00);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .course-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: white;
            font-weight: 600;
        }

        .course-details {
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
        }

        .detail-item i {
            color: #ffd700;
            width: 20px;
        }

        .trial-offer {
            background: rgba(255, 215, 0, 0.2);
            border-radius: 10px;
            padding: 10px;
            margin-top: 15px;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .trial-offer i {
            color: #ffd700;
        }

        .course-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }

        .feature-tag {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        /* Background Elements */
        .courses-bg-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .courses-bg-elements .bg-element {
            position: absolute;
            font-size: 2rem;
            opacity: 0.15;
            animation: float 6s ease-in-out infinite;
            color: rgba(255, 255, 255, 0.3);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }

        .courses-bg-elements .book {
            top: 10%;
            left: 5%;
            animation-delay: var(--delay);
        }

        .courses-bg-elements .graduation {
            top: 20%;
            right: 10%;
            animation-delay: var(--delay);
        }

        .courses-bg-elements .lightbulb {
            bottom: 30%;
            left: 8%;
            animation-delay: var(--delay);
        }

        .courses-bg-elements .computer {
            bottom: 15%;
            right: 15%;
            animation-delay: var(--delay);
        }

        .courses-bg-elements .science {
            top: 50%;
            left: 50%;
            transform: translateX(-50%);
            animation-delay: var(--delay);
        }

        @keyframes backgroundMove {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .courses-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 0 15px;
            }

            .courses-title {
                font-size: 2.2rem;
            }

            .courses-subtitle {
                font-size: 1.1rem;
            }

            .course-card {
                margin: 0 10px;
            }

            .course-intro {
                grid-template-columns: 1fr;
                gap: 50px;
                padding: 60px 20px;
            }

            .course-header h2 {
                font-size: 2.5rem;
            }

            .course-description {
                font-size: 1.1rem;
            }

            .feature-item {
                font-size: 1rem;
                padding: 15px 20px;
            }

            .register-course-btn {
                font-size: 1.1rem;
                padding: 16px 35px;
            }

            .course-image {
                width: 120px;
                height: 120px;
            }

            .python-animation,
            .scratch-animation,
            .stem-showcase {
                height: 350px;
            }

            .code-lines {
                position: static;
                margin-top: 20px;
            }

            .achievement-images {
                flex-direction: column;
                align-items: center;
            }

            .achievement-img {
                width: 100px;
                height: 100px;
            }

            .modal-content {
                padding: 20px;
                margin: 20px;
            }

            .final-registration-section {
                padding: 60px 20px;
            }

            .registration-header h2 {
                font-size: 2rem;
            }

            .registration-form-container {
                padding: 30px 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .final-submit-btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="register.html" class="active">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="index.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Course Registration Sections -->

    <!-- Python Course Section -->
    <section class="course-registration-section python-section" id="python-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <img src="../assets/images/courses/python-course.jpg" alt="Python Course" class="course-image">
                        <h2>Python - AI từ Cơ Bản đến Nâng Cao</h2>
                        <p class="course-description">Khám phá thế giới lập trình Python và Trí tuệ nhân tạo với những dự án thực tế hấp dẫn</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh THCS - THPT</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>250,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="scrollToRegistration('python')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="python-animation">
                        <div class="python-code-editor">
                            <div class="editor-header">
                                <div class="editor-dot red"></div>
                                <div class="editor-dot yellow"></div>
                                <div class="editor-dot green"></div>
                            </div>
                            <div class="python-code">
                                <div class="code-line comment"># Vẽ trái tim bằng Python</div>
                                <div class="code-line import"><span class="keyword">import</span> math</div>
                                <div class="code-line function"><span class="keyword">def</span> <span class="function">draw_heart</span>():</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">for</span> y <span class="keyword">in</span> range(6, -6, -1):</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">for</span> x <span class="keyword">in</span> range(-12, 13):</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;heart = (x*0.1)**2 + (y*0.15)**2 - 1</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">if</span> heart**3 - (x*0.1)**2*(y*0.15)**3 <= 0:</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">print</span>(<span class="string">"❤️"</span>, end=<span class="string">""</span>)</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">else</span>: <span class="keyword">print</span>(<span class="string">"  "</span>, end=<span class="string">""</span>)</div>
                                <div class="code-line">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="keyword">print</span>()</div>
                                <div class="code-line function">draw_heart()</div>
                            </div>
                        </div>

                        <div class="heart-output">
                            <div class="heart-art">
                                <div class="heart-line">    ❤️❤️❤️    ❤️❤️❤️    </div>
                                <div class="heart-line">  ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️  </div>
                                <div class="heart-line">❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️</div>
                                <div class="heart-line">❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️</div>
                                <div class="heart-line">❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️</div>
                                <div class="heart-line">❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️</div>
                                <div class="heart-line">  ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️  </div>
                                <div class="heart-line">    ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️    </div>
                                <div class="heart-line">      ❤️❤️❤️❤️❤️❤️❤️❤️      </div>
                                <div class="heart-line">        ❤️❤️❤️❤️❤️❤️        </div>
                                <div class="heart-line">          ❤️❤️❤️❤️          </div>
                                <div class="heart-line">            ❤️❤️            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scratch Course Section -->
    <section class="course-registration-section scratch-section" id="scratch-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <img src="../assets/images/courses/scratch-course.jpg" alt="Scratch Course" class="course-image">
                        <h2>Scratch - Tin Học Cơ Bản</h2>
                        <p class="course-description">Bước đầu tiên vào thế giới lập trình với Scratch - ngôn ngữ lập trình trực quan và thú vị</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh Tiểu học</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>300,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="scrollToRegistration('scratch')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="scratch-animation">
                        <div class="scratch-video-container">
                            <video class="scratch-video" id="scratchVideo" loop>
                                <source src="../assets/videos/scratch-demo.mp4" type="video/mp4">
                                Video demo Scratch
                            </video>
                            <div class="video-overlay"></div>
                            <div class="video-play-btn" id="playBtn" onclick="toggleVideo()">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-controls">
                                <button class="video-control-btn" onclick="toggleMute()" id="muteBtn">
                                    <i class="fas fa-volume-up"></i>
                                </button>
                                <button class="video-control-btn" onclick="toggleFullscreen()">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>

                        <div class="scratch-blocks-container">
                            <div class="scratch-block orange">when clicked</div>
                            <div class="scratch-block blue">move 10 steps</div>
                            <div class="scratch-block purple">say "Hello!"</div>
                            <div class="scratch-block green">repeat 10</div>
                        </div>

                        <div class="scratch-sparkles">
                            <div class="sparkle"></div>
                            <div class="sparkle"></div>
                            <div class="sparkle"></div>
                            <div class="sparkle"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- STEM Course Section -->
    <section class="course-registration-section stem-section" id="stem-registration">
        <div class="container">
            <div class="course-intro">
                <div class="course-content">
                    <div class="course-header">
                        <img src="../assets/images/courses/stem-course.jpg" alt="STEM Course" class="course-image">
                        <h2>Hỗ Trợ Nghiên Cứu KHKT - STEM</h2>
                        <p class="course-description">Phát triển tư duy khoa học và kỹ thuật thông qua các dự án nghiên cứu thực tế</p>
                    </div>

                    <div class="course-features">
                        <div class="feature-item">
                            <i class="fas fa-users"></i>
                            <span>Học sinh THCS - THPT</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>350,000 VNĐ/tháng</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <span>8 buổi/tháng, 90 phút/buổi</span>
                        </div>
                    </div>

                    <button class="register-course-btn" onclick="scrollToRegistration('stem')">
                        <i class="fas fa-user-plus"></i>
                        Đăng Ký Ngay
                    </button>
                </div>

                <div class="course-animation">
                    <div class="stem-showcase">
                        <div class="achievement-images">
                            <div class="achievement-img">
                                <div class="achievement-glow"></div>
                                <img src="../assets/images/achievements/covid-app-award.jpg" alt="COVID App Award">
                                <div class="achievement-label">🥇 Giải Nhất Quốc gia</div>
                            </div>
                            <div class="achievement-img">
                                <div class="achievement-glow"></div>
                                <img src="../assets/images/achievements/heart-disease-prediction-1.jpg" alt="Heart Disease Prediction">
                                <div class="achievement-label">🥈 Giải Nhì Tỉnh</div>
                            </div>
                            <div class="achievement-img">
                                <div class="achievement-glow"></div>
                                <img src="../assets/images/achievements/forest-fire-detection-1.jpg" alt="Forest Fire Detection">
                                <div class="achievement-label">🥉 Giải Ba Tỉnh</div>
                            </div>
                        </div>
                        <div class="stem-icons">
                            <div class="stem-icon"><i class="fas fa-flask"></i></div>
                            <div class="stem-icon"><i class="fas fa-robot"></i></div>
                            <div class="stem-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="stem-icon"><i class="fas fa-brain"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="courses-section" id="courses">
        <div class="container">
            <div class="courses-header" data-aos="fade-up">
                <h2 class="courses-title">
                    <span class="gradient-text">Khóa Học Của Chúng Tôi</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-graduation-cap"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="courses-subtitle">Chọn khóa học phù hợp với bạn và bắt đầu hành trình học tập</p>
            </div>

            <div class="courses-grid">
                <!-- Python-AI Course -->
                <div class="course-card python-course" data-aos="fade-up" data-aos-delay="100">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fab fa-python"></i>
                        </div>
                        <div class="course-badge">HOT</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Python - AI từ Cơ Bản đến Nâng Cao</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh THCS - THPT</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>250,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🐍 Python</span>
                            <span class="feature-tag">🤖 AI/ML</span>
                            <span class="feature-tag">💻 Coding</span>
                        </div>
                    </div>
                </div>

                <!-- Scratch Course -->
                <div class="course-card scratch-course" data-aos="fade-up" data-aos-delay="200">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <div class="course-badge">NEW</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Scratch - Tin Học Cơ Bản</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh Tiểu học</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>300,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🎨 Scratch</span>
                            <span class="feature-tag">📊 Excel</span>
                            <span class="feature-tag">📝 Word</span>
                            <span class="feature-tag">🖼️ Canva</span>
                        </div>
                    </div>
                </div>

                <!-- STEM Course -->
                <div class="course-card stem-course" data-aos="fade-up" data-aos-delay="300">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <div class="course-badge">PRO</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Hỗ Trợ Nghiên Cứu KHKT - STEM</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh THCS - THPT</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>350,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="detail-item special">
                                <i class="fas fa-star"></i>
                                <span>4 buổi code + 4 buổi slides</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🔬 KHKT</span>
                            <span class="feature-tag">📊 STEM</span>
                            <span class="feature-tag">📋 Báo cáo</span>
                            <span class="feature-tag">🏆 Dự án</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Animated Background Elements -->
        <div class="courses-bg-elements">
            <div class="bg-element book" style="--delay: 0s;">📚</div>
            <div class="bg-element graduation" style="--delay: 1s;">🎓</div>
            <div class="bg-element lightbulb" style="--delay: 2s;">💡</div>
            <div class="bg-element computer" style="--delay: 0.5s;">💻</div>
            <div class="bg-element science" style="--delay: 1.5s;">🔬</div>
        </div>
    </section>

    <!-- Final Registration Section -->
    <section class="final-registration-section" id="finalRegistration">
        <div class="container">
            <div class="registration-content">
                <div class="registration-header">
                    <h2>Liên Hệ Tư Vấn</h2>
                    <p>Điền thông tin để chúng tôi tư vấn và hỗ trợ bạn chọn khóa học phù hợp nhất</p>
                </div>

                <div class="registration-form-container">
                    <form id="finalRegistrationForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="finalStudentName">Tên Học Viên <span class="required">*</span></label>
                                <input type="text" id="finalStudentName" name="studentName" required>
                            </div>

                            <div class="form-group">
                                <label for="finalBirthDate">Ngày Sinh <span class="required">*</span></label>
                                <input type="date" id="finalBirthDate" name="birthDate" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="finalParentName">Tên Phụ Huynh <span class="required">*</span></label>
                                <input type="text" id="finalParentName" name="parentName" required>
                            </div>

                            <div class="form-group">
                                <label for="finalParentPhone">Số Điện Thoại Phụ Huynh <span class="required">*</span></label>
                                <input type="tel" id="finalParentPhone" name="parentPhone" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="finalCourseSelect">Chọn Khóa Học <span class="required">*</span></label>
                            <select id="finalCourseSelect" name="course" required>
                                <option value="">-- Chọn khóa học --</option>
                                <option value="python">Python - AI từ Cơ Bản đến Nâng Cao (250,000 VNĐ/tháng)</option>
                                <option value="scratch">Scratch - Tin Học Cơ Bản (300,000 VNĐ/tháng)</option>
                                <option value="stem">Hỗ Trợ Nghiên Cứu KHKT - STEM (350,000 VNĐ/tháng)</option>
                            </select>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="final-submit-btn">
                                <i class="fas fa-paper-plane"></i>
                                Gửi Đăng Ký
                            </button>
                        </div>
                    </form>

                    <div class="success-message" id="finalSuccessMessage" style="display:none;">
                        <i class="fas fa-check-circle"></i>
                        <h3>Đăng ký thành công!</h3>
                        <p>Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất để xác nhận và cung cấp thông tin chi tiết về khóa học.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Registration Form Modal -->
    <div class="registration-modal" id="registrationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Đăng Ký Khóa Học</h2>
                <button class="close-modal" onclick="hideRegistrationForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="registrationForm">
                <input type="hidden" id="selectedCourse" name="course">

                <div class="form-group">
                    <label for="studentName">Tên Học Viên <span class="required">*</span></label>
                    <input type="text" id="studentName" name="studentName" required>
                </div>

                <div class="form-group">
                    <label for="birthDate">Ngày Tháng Năm Sinh <span class="required">*</span></label>
                    <input type="date" id="birthDate" name="birthDate" required>
                </div>

                <div class="form-group">
                    <label for="parentName">Tên Phụ Huynh <span class="required">*</span></label>
                    <input type="text" id="parentName" name="parentName" required>
                </div>

                <div class="form-group">
                    <label for="parentPhone">Số Điện Thoại Phụ Huynh <span class="required">*</span></label>
                    <input type="tel" id="parentPhone" name="parentPhone" required>
                </div>

                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="hideRegistrationForm()">Hủy</button>
                    <button type="submit" class="submit-btn">Gửi Đăng Ký</button>
                </div>
            </form>

            <div class="success-message" id="successMessage" style="display:none;">
                <i class="fas fa-check-circle"></i>
                <h3>Đăng ký thành công!</h3>
                <p>Chúng tôi sẽ liên hệ với bạn sớm để xác nhận và cung cấp thông tin chi tiết.</p>
            </div>
        </div>
    </div>

    <!-- Slide Registration Form -->
    <div class="registration-form-slide" id="slideForm">
        <button class="slide-close-btn" onclick="hideSlideForm()">
            <i class="fas fa-times"></i>
        </button>

        <div class="slide-form-content">
            <div class="slide-form-header">
                <h3 id="slideFormTitle">Đăng Ký Khóa Học</h3>
                <p>Điền thông tin để đăng ký ngay!</p>
            </div>

            <form id="slideRegistrationForm">
                <input type="hidden" id="slideSelectedCourse" name="course">

                <div class="form-group">
                    <label for="slideStudentName">Tên Học Viên <span class="required">*</span></label>
                    <input type="text" id="slideStudentName" name="studentName" required>
                </div>

                <div class="form-group">
                    <label for="slideBirthDate">Ngày Sinh <span class="required">*</span></label>
                    <input type="date" id="slideBirthDate" name="birthDate" required>
                </div>

                <div class="form-group">
                    <label for="slideParentName">Tên Phụ Huynh <span class="required">*</span></label>
                    <input type="text" id="slideParentName" name="parentName" required>
                </div>

                <div class="form-group">
                    <label for="slideParentPhone">SĐT Phụ Huynh <span class="required">*</span></label>
                    <input type="tel" id="slideParentPhone" name="parentPhone" required>
                </div>

                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="hideSlideForm()">Hủy</button>
                    <button type="submit" class="submit-btn">Gửi Đăng Ký</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getFirestore, collection, addDoc } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const db = getFirestore(app);

        // Course titles mapping
        const courseTitles = {
            'python': 'Python - AI từ Cơ Bản đến Nâng Cao',
            'scratch': 'Scratch - Tin Học Cơ Bản',
            'stem': 'Hỗ Trợ Nghiên Cứu KHKT - STEM'
        };

        // Show registration form modal
        window.showRegistrationForm = function(courseType) {
            const modal = document.getElementById('registrationModal');
            const modalTitle = document.getElementById('modalTitle');
            const selectedCourse = document.getElementById('selectedCourse');

            modalTitle.textContent = `Đăng Ký - ${courseTitles[courseType]}`;
            selectedCourse.value = courseType;
            modal.style.display = 'block';

            // Reset form and hide messages
            document.getElementById('registrationForm').reset();
            document.getElementById('successMessage').style.display = 'none';
        };

        // Hide registration form modal
        window.hideRegistrationForm = function() {
            const modal = document.getElementById('registrationModal');
            modal.style.display = 'none';
        };

        // Show slide form
        window.showSlideForm = function(courseType) {
            const slideForm = document.getElementById('slideForm');
            const slideFormTitle = document.getElementById('slideFormTitle');
            const slideSelectedCourse = document.getElementById('slideSelectedCourse');

            slideFormTitle.textContent = `Đăng Ký - ${courseTitles[courseType]}`;
            slideSelectedCourse.value = courseType;
            slideForm.classList.add('active');

            // Reset form
            document.getElementById('slideRegistrationForm').reset();
        };

        // Hide slide form
        window.hideSlideForm = function() {
            const slideForm = document.getElementById('slideForm');
            slideForm.classList.remove('active');
        };

        // Scroll to registration section
        window.scrollToRegistration = function(courseType) {
            const finalRegistration = document.getElementById('finalRegistration');
            const courseSelect = document.getElementById('finalCourseSelect');

            // Set the course in the select
            courseSelect.value = courseType;

            // Smooth scroll to registration section
            finalRegistration.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Add highlight effect
            finalRegistration.style.animation = 'highlightSection 2s ease-in-out';
            setTimeout(() => {
                finalRegistration.style.animation = '';
            }, 2000);
        };

        // Video control functions
        window.toggleVideo = function() {
            const video = document.getElementById('scratchVideo');
            const playBtn = document.getElementById('playBtn');

            if (video.paused) {
                video.play();
                playBtn.innerHTML = '<i class="fas fa-pause"></i>';
            } else {
                video.pause();
                playBtn.innerHTML = '<i class="fas fa-play"></i>';
            }
        };

        window.toggleMute = function() {
            const video = document.getElementById('scratchVideo');
            const muteBtn = document.getElementById('muteBtn');

            if (video.muted) {
                video.muted = false;
                muteBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
            } else {
                video.muted = true;
                muteBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
            }
        };

        window.toggleFullscreen = function() {
            const video = document.getElementById('scratchVideo');

            if (video.requestFullscreen) {
                video.requestFullscreen();
            } else if (video.webkitRequestFullscreen) {
                video.webkitRequestFullscreen();
            } else if (video.msRequestFullscreen) {
                video.msRequestFullscreen();
            }
        };

        // Close modal when clicking outside
        document.getElementById('registrationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideRegistrationForm();
            }
        });

        // Handle form submission
        const form = document.getElementById('registrationForm');
        const successMessage = document.getElementById('successMessage');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                // Prepare form data
                const formData = {
                    studentName: document.getElementById('studentName').value,
                    birthDate: document.getElementById('birthDate').value,
                    parentName: document.getElementById('parentName').value,
                    parentPhone: document.getElementById('parentPhone').value,
                    course: document.getElementById('selectedCourse').value,
                    courseName: courseTitles[document.getElementById('selectedCourse').value],
                    registrationDate: new Date().toISOString(),
                    status: 'pending' // pending, approved, rejected
                };

                // Submit to Firebase
                const docRef = await addDoc(collection(db, "course_registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);

                // Hide form and show success message
                form.style.display = 'none';
                successMessage.style.display = 'block';

                // Auto close modal after 3 seconds
                setTimeout(() => {
                    hideRegistrationForm();
                    form.style.display = 'block';
                    successMessage.style.display = 'none';
                    form.reset();
                }, 3000);

            } catch (error) {
                console.error("Error processing registration: ", error);
                alert(`Lỗi: ${error.message}`);
            }
        });

        // Handle slide form submission
        const slideForm = document.getElementById('slideRegistrationForm');

        slideForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                // Prepare form data
                const formData = {
                    studentName: document.getElementById('slideStudentName').value,
                    birthDate: document.getElementById('slideBirthDate').value,
                    parentName: document.getElementById('slideParentName').value,
                    parentPhone: document.getElementById('slideParentPhone').value,
                    course: document.getElementById('slideSelectedCourse').value,
                    courseName: courseTitles[document.getElementById('slideSelectedCourse').value],
                    registrationDate: new Date().toISOString(),
                    status: 'pending' // pending, approved, rejected
                };

                // Submit to Firebase
                const docRef = await addDoc(collection(db, "course_registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);

                // Show success and close form
                alert('Đăng ký thành công! Chúng tôi sẽ liên hệ với bạn sớm.');
                hideSlideForm();

            } catch (error) {
                console.error("Error processing registration: ", error);
                alert(`Lỗi: ${error.message}`);
            }
        });

        // Smooth scrolling for course sections
        function scrollToCourse(courseId) {
            document.getElementById(courseId + '-registration').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Handle final form submission
        const finalForm = document.getElementById('finalRegistrationForm');
        const finalSuccessMessage = document.getElementById('finalSuccessMessage');

        finalForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            try {
                // Prepare form data
                const formData = {
                    studentName: document.getElementById('finalStudentName').value,
                    birthDate: document.getElementById('finalBirthDate').value,
                    parentName: document.getElementById('finalParentName').value,
                    parentPhone: document.getElementById('finalParentPhone').value,
                    course: document.getElementById('finalCourseSelect').value,
                    courseName: courseTitles[document.getElementById('finalCourseSelect').value],
                    registrationDate: new Date().toISOString(),
                    status: 'pending'
                };

                // Submit to Firebase
                const docRef = await addDoc(collection(db, "course_registrations"), formData);
                console.log("Registration successful with ID: ", docRef.id);

                // Hide form and show success message
                finalForm.style.display = 'none';
                finalSuccessMessage.style.display = 'block';

                // Reset form after 5 seconds
                setTimeout(() => {
                    finalForm.style.display = 'block';
                    finalSuccessMessage.style.display = 'none';
                    finalForm.reset();
                }, 5000);

            } catch (error) {
                console.error("Error processing registration: ", error);
                alert(`Lỗi: ${error.message}`);
            }
        });

        // Add highlight animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes highlightSection {
                0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
                50% { box-shadow: 0 0 0 20px rgba(52, 152, 219, 0.3); }
                100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
            }
        `;
        document.head.appendChild(style);

        // Check URL parameters for direct course access
        const urlParams = new URLSearchParams(window.location.search);
        const course = urlParams.get('course');
        if (course && courseTitles[course]) {
            setTimeout(() => {
                scrollToRegistration(course);
            }, 500);
        }
    </script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>